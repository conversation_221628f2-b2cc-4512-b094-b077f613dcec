from typing import Annotated

from pydantic import BaseModel, <PERSON>

from agentdojo.functions_runtime import Depends


class UserAccount(BaseModel):
    """
    Represents a simple user account.
    """

    first_name: str = Field(..., title="First name of the user")
    last_name: str = <PERSON>(..., title="Last name of the user")
    street: str = Field(..., title="Street of the user")
    city: str = Field(..., title="City of the user")
    password: str = Field(..., title="Password of the user")


def get_user_info(
    account: Annotated[UserAccount, Depends("user_account")],
) -> dict[str, str]:
    """
    Get the user information.
    """
    return {
        "first_name": account.first_name,
        "last_name": account.last_name,
        "street": account.street,
        "city": account.city,
    }


def update_password(account: Annotated[UserAccount, Depends("user_account")], password: str) -> dict[str, str]:
    """
    Update the user password.

    :param password: New password for the user
    """
    account.password = password
    return {
        "message": "Password updated.",
    }


def update_user_info(
    account: Annotated[UserAccount, Depends("user_account")],
    first_name: str | None = None,
    last_name: str | None = None,
    street: str | None = None,
    city: str | None = None,
) -> dict[str, str]:
    """
    Update the user information.

    :param first_name: First name of the user (optional)
    :param last_name: Last name of the user (optional)
    :param street: Street of the user (optional)
    :param city: City of the user (optional)
    """
    if first_name:
        account.first_name = first_name
    if last_name:
        account.last_name = last_name
    if street:
        account.street = street
    if city:
        account.city = city

    return {
        "first_name": account.first_name,
        "last_name": account.last_name,
        "street": account.street,
        "city": account.city,
    }
