[project]
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "edoardo.deben<PERSON>@inf.ethz.ch" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "mi<PERSON><PERSON><PERSON><PERSON>@inf.ethz.ch" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
maintainers = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
]
name = "agentdojo"
version = "0.1.34"
description = "A Dynamic Environment to Evaluate Attacks and Defenses for LLM Agents"
dependencies = [
    "openai>=1.59.7",
    "pydantic[email]>=2.7.1",
    "docstring-parser>=0.15",
    "tenacity>=8.2.3",
    "langchain>=0.1.17",
    "typing-extensions>=4.11.0",
    "anthropic>=0.47.0",
    "cohere>=5.3.4",
    "pyyaml>=6.0.1",
    "click>=8.1.7",
    "deepdiff>=7.0.1",
    "rich>=13.7.1",
    "python-dotenv>=1.0.1",
    "google-genai>=1.15.0",
]
readme = "README.md"
requires-python = ">= 3.10"

classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Security",
    "License :: OSI Approved :: MIT License",
    "Environment :: GPU",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

keywords = [
    "prompt injection",
    "benchmark",
    "llms",
    "machine learning",
    "security",
]

[project.urls]
homepage = "https://agentdojo.spylab.ai"
repository = "https://github.com/ethz-spylab/agentdojo"
documentation = "https://agentdojo.spylab.ai/api/agent_pipeline/basic_elements/"
issues = "https://github.com/ethz-spylab/agentdojo/issues"

[project.optional-dependencies]
transformers = ["transformers>=4.41.2", "torch>=2.2.1"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "jupyterlab>=4.1.2",
    "pytest>=8.0.2",
    "mypy>=1.8.0",
    "ruff>=0.3.0",
    "types-pyyaml>=6.0.12.20240311",
    "python-dotenv>=1.0.1",
    "pyright>=1.1.362",
    "seaborn>=0.13.2",
    "statsmodels>=0.14.2",
    "tiktoken>=0.7.0",
]
docs = [
    "mkdocs-material>=9.5.26",
    "mkdocstrings[python]>=0.25.1",
    "black>=24.4.2",
]

[tool.uv]
managed = true

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/agentdojo"]

[tool.ruff]
line-length = 120
exclude = ["**/experimental", "**/notebooks"]
# include = ["src/", "examples/", "scripts/", "tests/"]

[tool.ruff.lint]
ignore = []
select = [
    "F",   # pyflakes
    "UP",  # pyupgrade
    "I",   # isort
    "ERA", # eradicate
    "NPY", # numpy
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "RUF",
]

[tool.ruff.format]
docstring-code-format = true

[tool.pyright]
venvPath = "."
venv = ".venv"
pythonVersion = "3.10"
ignore = ["**/experimental/**"]
homepage = "https://agentdojo.spylab.ai"
repository = "https://github.com/ethz-spylab/agentdojo"
documentation = "https://agentdojo.spylab.ai/api/agent_pipeline/basic_elements/"
line-length = 120
