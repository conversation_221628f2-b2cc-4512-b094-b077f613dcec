# --8<-- [start:suite_creation]
from pathlib import Path

from agentdojo.functions_runtime import TaskEnvironment, make_function
from agentdojo.task_suite import TaskSuite
from counter_benchmark.suites.tools import Counter, get_description, get_value, increment, increment_by


class CounterEnvironment(TaskEnvironment):
    counter: Counter


TOOLS = [increment, increment_by, get_value, get_description]
task_suite = TaskSuite(
    "counter",  # (2)!
    CounterEnvironment,
    [make_function(tool) for tool in TOOLS],  # (3)!
    Path("counter_benchmark/data/suites/counter"),  # (4)!
)
# --8<-- [end:suite_creation]
