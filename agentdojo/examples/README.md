# Examples

This directory contains some examples of how to use AgentDojo:

- [counter_benchmark](counter_benchmark): A simple example of how to create a new benchmark using AgentDojo.
- [pipeline](pipeline.py): A simple example of how to create a new agent pipeline using AgentDojo.
- [attack](attack.py): A simple example of how to create a new attack using AgentDojo.
- [functions_runtime.py](functions_runtime.py): A simple example of how to use AgentDojo's functions runtime.
