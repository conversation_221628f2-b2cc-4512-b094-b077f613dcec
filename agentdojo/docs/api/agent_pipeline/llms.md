# LLMs

## API-based tool-calling LLMs

These pipeline elements use tool-calling LLMs via the official tool-calling API.

::: agentdojo.agent_pipeline.OpenAILLM
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.AnthropicLLM
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.GoogleLLM
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.CohereLLM
    options:
        filters: ["!query"]
        heading_level: 3

## Prompt-based tool-calling LLMs

These pipeline elements use tool-calling LLMs via a system prompt that instructs
them to use tools, and provides them documentation as part of the system prompt.

::: agentdojo.agent_pipeline.BasePromptingLLM
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.PromptingLLM
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.PromptingLLM._tool_calling_prompt
    options:
        filters: ["!query"]
        heading_level: 4
