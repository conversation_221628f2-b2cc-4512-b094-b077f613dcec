# Agent pipeline

::: agentdojo.agent_pipeline.BasePipelineElement


## Base elements

::: agentdojo.agent_pipeline.AgentPipeline
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.PipelineConfig
    options:
        filters: ["!query"]
        heading_level: 4

::: agentdojo.agent_pipeline.InitQuery
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.GroundTruthPipeline
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.SystemMessage
    options:
        filters: ["!query"]
        heading_level: 3

## Function execution elements

::: agentdojo.agent_pipeline.ToolsExecutor
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.ToolsExecutionLoop
    options:
        filters: ["!query"]
        heading_level: 3

::: agentdojo.agent_pipeline.tool_execution.tool_result_to_str
    options:
        filters: ["!query"]
        heading_level: 3

## Exceptions

::: agentdojo.agent_pipeline.AbortAgentError
    options:
        filters: ["!query"]
        heading_level: 3


## Available models and defenses

::: agentdojo.models.ModelsEnum
    options:
        filters: ["!query"]
        heading_level: 3


::: agentdojo.agent_pipeline.agent_pipeline.DEFENSES
    options:
        filters: ["!query"]
        heading_level: 3
