---
hide:
  - navigation
  - toc
---

# AgentDojo Results

Here are all the results from different combinations of models, defenses, and attacks that we ran on our benchmark.

!!! note
    Please note that this is **not** a leaderboard because we did not attack all models with all attacks and defenses, so it would be too difficult to have a fair comparison across models/defenses/attacks. Rows are sorted by default the `Date` column.

## How do I add my results?

Please run your attacks/models/defenses on the benchmark within a fork of the [repository](https://github.com/ethz-spylab/agentdojo), and open a PR with your results. We will merge your PR and your results will be displayed here. The PR should also include a brief description of the attack/defense/model you are submitting, and an implementation of the attack/defense/model in the `agentdojo` repository.

## How to inspect the results?

You can click the "Explore &rarr;" button to the right of each row to see full runtime traces of all runs including agent trajectories, attacks and defenses in the [Invariant Explorer](https://explorer.invariantlabs.ai) trace viewing tool.

--8<-- "results-table.html"

<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script>
new DataTable('#results-table', {
    pageLength: 25,
    responsive: true,
    order: [[7, 'desc']],
    language: {
      searchPlaceholder: "Models, defenses, attacks..."
    },
});
</script>
