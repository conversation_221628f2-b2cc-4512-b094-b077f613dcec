---
hide:
  - navigation
---

# Changelog

## v0.1.33

### Features

- Add support for `max_tokens` parameter for `GoogleLLM`

### Breaking Changes

None

### Bug Fixes

None

## v0.1.33

### Features

- Add support for new Google SDK (#84) by @dedeswim

### Breaking Changes

- The use of `GoogleLLM` without passing a client as argument is now deprecated (but still supported)

### Bug Fixes

None

## v0.1.32

### Features

None

### Breaking Changes

None

### Bug Fixes

- Fix issue with TogetherAI LLM (#82) by @dedeswim.

## v0.1.31

### Features

None

### Breaking Changes

None

### Bug Fixes

- Fix issue with OpenAI LLM (#77) by @thavens.

## v0.1.30

### Features

- Add support for multi-block chat messages (#63) by @dedeswim.
- Add support for reasoning models (Anthropic and OpenAI) and arguments (#60) by @dedeswim.
- Add support for vLLM models (#68) by @normster and @thavens.

### Breaking Changes

- Single-block chat messages are not supported anymore. Old chat logs are still correctly loaded, but all `ChatMessage`'s `'content'` usage should be updated. A unique text can be extracted from `ChatMessage['content']` by using the `agentdojo.types.get_text_content_as_str` utility function. Check #63 for more information.

### Bug Fixes

- Fix some issues in Benchmark v1.2 (#75) by @dedeswim and @EricWinsorDSIT

## v0.1.22

### Features

- Add Gemini 1.5 Flash and Pro 002 and Claude Sonnet 3.5 October release by [@maxwbuckley](https://github.com/maxwbuckley).

### Breaking Changes

- None

### Bug Fixes

- Fix some JSON Schema formats for Google models by by [@maxwbuckley](https://github.com/maxwbuckley).
- Fix benchmark version inconsistencies.


## v0.1.21

### Features

- None

### Breaking Changes

- None

### Bug Fixes

- Fix several bugs in tasks verifications, as spotted by [@cw00h](https://github.com/cw00h).

## v0.1.20

### Features

- Save the type itself to [`Function.return_type`][agentdojo.functions_runtime.Function.return_type] instead of its string representation to avoid losing information.

### Breaking Changes

- None

### Bug Fixes

- None.

## v0.1.19

### Features

- None.

### Breaking Changes

- None

### Bug Fixes

- Fix missing support for generic return types (e.g., `list[str]`) in [`Function.return_type`][agentdojo.functions_runtime.Function.return_type].

## v0.1.18

### Features

- Now [`Function`][agentdojo.functions_runtime.Function] stores also the return type of the function and the full docstring.

### Breaking Changes

- None

### Bug Fixes

- None

## v0.1.17

### Features

- None

### Breaking Changes

- None

### Bug Fixes

- Fix breaking changes due to updated APIs from some LLM providers.

## v0.1.16

### Features

- Add the possibility to pass a custom pipeline element object as an LLM to a `PipelineConfig`.

### Breaking Changes

- None

### Bug Fixes

- Updated some checks in the banking suite.

## v0.1.15

### Features

- None

### Breaking Changes

- None

### Bug Fixes

- Fix the security checks for Workspace's injections tasks 4 and 5. The checks were not precise
  and injection tasks 4 would fail in some edge cases even if the model was executing it correctly.

## v0.1.14

### Features

None

### Breaking Changes

- `Ellipsis` is not supported anymore as a type for function calls. This was done
to simplify the code as any value can be passed as an argument to a function call,
since is going to be discarded by the utility/security checks anyways.

### Bug Fixes

- Fix issue with `FunctionCall` not being serializable when saving to JSON. This issue
  was introuced in v0.1.13 when the `FunctionCall` object was changed from a `TypedDict`

## v0.1.13

### Features

- Add support for nested function calls in the [`FunctionCall`][agentdojo.functions_runtime.FunctionCall] object.
  This allows for more complex function calls: if any of the `args` in a [`FunctionCall`][agentdojo.functions_runtime.FunctionCall]
  object is another [`FunctionCall`][agentdojo.functions_runtime.FunctionCall], the function call will be executed
  recursively.

### Breaking Changes

None

### Bug Fixes

None

## v0.1.12

### Features

None

### Breaking Changes

- Change [`FunctionCall`][agentdojo.functions_runtime.FunctionCall] from a
  [`TypedDict`][typing.TypedDict] to a Pydantic [`BaseModel`][pydantic.BaseModel].
  This was done to allow for distinguishing function calls at runtime (in preparation)
  for supporting nested function calls. This affects users who are implementing new LLMs,
  new runtimes, and pipeline components that use function calls. To update your code,
  you will need to access the [`function`][agentdojo.functions_runtime.FunctionCall.function]
  and [`args`][agentdojo.functions_runtime.FunctionCall.args] attributes of the
  [`FunctionCall`][agentdojo.functions_runtime.FunctionCall] object instead of using
  dictionary access.

### Bug Fixes

None
