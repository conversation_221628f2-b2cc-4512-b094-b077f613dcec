from agentdojo.benchmark import convert_old_messages_format


def test_convert_old_messages_format():
    # Test with various message types
    old_messages = [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi there", "tool_calls": None},
        {
            "role": "tool",
            "content": "123",
            "tool_call": {"function": "add", "args": {"a": 1, "b": 2}},
            "tool_call_id": None,
            "error": None,
        },
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "assistant", "content": None, "tool_calls": [{"function": "multiply", "args": {"a": 3, "b": 4}}]},
    ]

    converted = convert_old_messages_format(old_messages)

    # Check that each message was properly converted
    assert len(converted) == 5

    # User message
    assert converted[0]["role"] == "user"
    assert len(converted[0]["content"]) == 1
    assert converted[0]["content"][0]["type"] == "text"
    assert converted[0]["content"][0]["content"] == "Hello"

    # Assistant message with content
    assert converted[1]["role"] == "assistant"
    assert converted[1]["content"] is not None
    assert len(converted[1]["content"]) == 1
    assert converted[1]["content"][0]["type"] == "text"
    assert converted[1]["content"][0]["content"] == "Hi there"
    assert converted[1]["tool_calls"] is None

    # Tool message
    assert converted[2]["role"] == "tool"
    assert len(converted[2]["content"]) == 1
    assert converted[2]["content"][0]["type"] == "text"
    assert converted[2]["content"][0]["content"] == "123"
    assert converted[2]["tool_call"]["function"] == "add"

    # System message
    assert converted[3]["role"] == "system"
    assert len(converted[3]["content"]) == 1
    assert converted[3]["content"][0]["type"] == "text"
    assert converted[3]["content"][0]["content"] == "You are a helpful assistant"

    # Assistant message with tool calls but no content
    assert converted[4]["role"] == "assistant"
    assert converted[4]["content"] is None
    assert converted[4]["tool_calls"] is not None

    # Check that original messages are not modified
    assert isinstance(old_messages[0]["content"], str)

    # Check return type is list[ChatMessage]
    assert isinstance(converted, list)
    for message in converted:
        assert isinstance(message, dict)
